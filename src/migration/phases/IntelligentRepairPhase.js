const MigrationPhase = require('../MigrationPhase');
const BuildFixer = require('../../build/buildFixer');
const AIRepairer = require('../../ai/aiRepairer');
const ESLintFixer = require('../../lint/eslintFixer');
const chalk = require('chalk');

/**
 * 智能修复与优化阶段
 * 核心 AI 修复逻辑和迭代修复循环
 */
class IntelligentRepairPhase extends MigrationPhase {
  constructor(projectPath, options = {}) {
    super('智能修复与优化', projectPath, options);
    this.sourceToTargetMode = options.sourceToTargetMode || false;
    this.targetProjectPath = options.targetProjectPath;
    this.workingPath = this.sourceToTargetMode ? this.targetProjectPath : this.projectPath;
    this.maxAttempts = options.maxRepairAttempts || 3;
    this.buildCommand = options.buildCommand || 'npm run build';
  }

  /**
   * 获取阶段依赖
   */
  getDependencies() {
    return ['代码迁移与转换'];
  }

  /**
   * 执行智能修复阶段
   */
  async execute(context) {
    const result = {
      success: false,
      buildSuccess: false,
      repairAttempts: 0,
      aiRepairSuccess: false,
      eslintFixSuccess: false,
      finalBuildSuccess: false,
      errors: [],
      fixedIssues: []
    };

    try {
      console.log(chalk.blue('🤖 开始智能修复循环...'));

      // 获取前一阶段的失败文件
      const failedFiles = this.getFailedFilesFromContext(context);
      
      // 执行迭代修复循环
      const repairResult = await this.executeRepairLoop(failedFiles);
      
      // 更新结果
      Object.assign(result, repairResult);
      
      // 如果构建成功，执行最终优化
      if (result.buildSuccess) {
        console.log(chalk.blue('✨ 执行最终优化...'));
        await this.performFinalOptimizations(result);
      }

      result.success = result.buildSuccess;
      return result;

    } catch (error) {
      result.success = false;
      result.error = error.message;
      result.errors.push(error.message);
      throw error;
    }
  }

  /**
   * 从上下文获取失败文件
   */
  getFailedFilesFromContext(context) {
    const failedFiles = [];
    
    // 从代码迁移阶段获取失败文件
    if (context['代码迁移与转换']?.failedFilesList) {
      failedFiles.push(...context['代码迁移与转换'].failedFilesList);
    }

    return failedFiles;
  }

  /**
   * 执行修复循环
   */
  async executeRepairLoop(initialFailedFiles) {
    const result = {
      buildSuccess: false,
      repairAttempts: 0,
      aiRepairSuccess: false,
      eslintFixSuccess: false,
      errors: [],
      fixedIssues: []
    };

    let currentFailedFiles = [...initialFailedFiles];
    
    for (let attempt = 1; attempt <= this.maxAttempts; attempt++) {
      result.repairAttempts = attempt;
      console.log(chalk.blue(`\n🔄 修复尝试 ${attempt}/${this.maxAttempts}...`));

      try {
        // 1. 构建检测
        console.log(chalk.blue('  🏗️  检测构建状态...'));
        const buildResult = await this.attemptBuild();
        
        if (buildResult.success) {
          result.buildSuccess = true;
          result.fixedIssues.push(`第 ${attempt} 次尝试构建成功`);
          console.log(chalk.green('  ✅ 构建成功！'));
          break;
        } else {
          console.log(chalk.yellow(`  ⚠️  构建失败，发现 ${buildResult.errors.length} 个错误`));
          result.errors.push(...buildResult.errors);
        }

        // 2. AI 智能修复
        if (this.options.aiApiKey && !this.options.skipAIRepair) {
          console.log(chalk.blue('  🤖 AI 智能修复...'));
          const aiResult = await this.performAIRepair(buildResult.errors, currentFailedFiles);
          result.aiRepairSuccess = aiResult.success;
          if (aiResult.fixedFiles > 0) {
            result.fixedIssues.push(`AI 修复了 ${aiResult.fixedFiles} 个文件`);
          }
        }

        // 3. ESLint 自动修复
        if (!this.options.skipESLint) {
          console.log(chalk.blue('  🔧 ESLint 自动修复...'));
          const eslintResult = await this.performESLintFix();
          result.eslintFixSuccess = eslintResult.success;
          if (eslintResult.filesFixed > 0) {
            result.fixedIssues.push(`ESLint 修复了 ${eslintResult.filesFixed} 个文件`);
          }
        }

        // 4. 更新失败文件列表
        currentFailedFiles = await this.updateFailedFilesList(buildResult.errors);

      } catch (error) {
        console.log(chalk.red(`  ❌ 修复尝试 ${attempt} 失败: ${error.message}`));
        result.errors.push(`修复尝试 ${attempt} 失败: ${error.message}`);
        
        // 如果是最后一次尝试，抛出错误
        if (attempt === this.maxAttempts) {
          throw error;
        }
      }
    }

    return result;
  }

  /**
   * 尝试构建项目
   */
  async attemptBuild() {
    try {
      const buildFixer = new BuildFixer(this.workingPath, {
        buildCommand: this.buildCommand,
        timeout: 60000, // 60秒超时
        captureOutput: true
      });

      // 只执行构建检测，不执行修复
      const result = await buildFixer.detectBuildErrors();
      
      return {
        success: result.success,
        errors: result.errors || [],
        output: result.output || ''
      };

    } catch (error) {
      return {
        success: false,
        errors: [error.message],
        output: ''
      };
    }
  }

  /**
   * 执行 AI 修复
   */
  async performAIRepair(buildErrors, failedFiles) {
    try {
      const aiRepairer = new AIRepairer({ 
        apiKey: this.options.aiApiKey,
        maxRetries: 2
      });

      if (!aiRepairer.isEnabled()) {
        console.log(chalk.yellow('  ⚠️  AI 修复不可用（缺少 API Key）'));
        return { success: false, fixedFiles: 0 };
      }

      // 合并构建错误和失败文件
      const allIssues = [
        ...buildErrors.map(error => ({
          type: 'build_error',
          error: error,
          file: this.extractFileFromError(error)
        })),
        ...failedFiles.map(file => ({
          type: 'migration_failure',
          error: file.error,
          file: file.file
        }))
      ];

      if (allIssues.length === 0) {
        return { success: true, fixedFiles: 0 };
      }

      console.log(chalk.blue(`    准备修复 ${allIssues.length} 个问题...`));
      
      const result = await aiRepairer.repairFailedFiles(allIssues, this.workingPath);
      
      return {
        success: result.success || false,
        fixedFiles: result.success || 0,
        details: result
      };

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  AI 修复失败: ${error.message}`));
      return { success: false, fixedFiles: 0, error: error.message };
    }
  }

  /**
   * 执行 ESLint 修复
   */
  async performESLintFix() {
    try {
      const eslintFixer = new ESLintFixer(this.workingPath);

      // 检查 ESLint 是否可用
      if (!await eslintFixer.isESLintAvailable()) {
        console.log(chalk.yellow('  ⚠️  ESLint 不可用'));
        return { success: false, filesFixed: 0 };
      }

      const result = await eslintFixer.fix();
      
      return {
        success: true,
        filesFixed: result.filesFixed || 0,
        errorsFixed: result.errorsFixed || 0
      };

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  ESLint 修复失败: ${error.message}`));
      return { success: false, filesFixed: 0, error: error.message };
    }
  }

  /**
   * 更新失败文件列表
   */
  async updateFailedFilesList(buildErrors) {
    const failedFiles = [];
    
    // 从构建错误中提取文件信息
    buildErrors.forEach(error => {
      const file = this.extractFileFromError(error);
      if (file) {
        failedFiles.push({
          file,
          error,
          errorType: 'build_error'
        });
      }
    });

    return failedFiles;
  }

  /**
   * 从错误信息中提取文件路径
   */
  extractFileFromError(error) {
    // 尝试从错误信息中提取文件路径
    const patterns = [
      /ERROR in (.+?):/,
      /Module not found: Error: Can't resolve '(.+?)'/,
      /Failed to compile (.+)/,
      /at (.+?):\d+:\d+/
    ];

    for (const pattern of patterns) {
      const match = error.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * 执行最终优化
   */
  async performFinalOptimizations(result) {
    try {
      // 1. 代码格式化
      console.log(chalk.blue('  📝 代码格式化...'));
      await this.formatCode();

      // 2. 性能优化建议
      console.log(chalk.blue('  ⚡ 生成性能优化建议...'));
      const optimizations = await this.generateOptimizationSuggestions();
      result.optimizationSuggestions = optimizations;

      // 3. 最终构建验证
      console.log(chalk.blue('  🔍 最终构建验证...'));
      const finalBuild = await this.attemptBuild();
      result.finalBuildSuccess = finalBuild.success;

    } catch (error) {
      console.log(chalk.yellow(`⚠️  最终优化失败: ${error.message}`));
    }
  }

  /**
   * 代码格式化
   */
  async formatCode() {
    // 这里可以集成 Prettier 或其他代码格式化工具
    console.log(chalk.gray('    代码格式化功能待实现'));
  }

  /**
   * 生成优化建议
   */
  async generateOptimizationSuggestions() {
    const suggestions = [
      '考虑使用 Vue 3 的 Composition API 重构复杂组件',
      '检查是否可以移除不必要的依赖',
      '优化打包配置以减少包体积',
      '使用 Vue 3 的新特性如 Teleport、Suspense 等'
    ];

    return suggestions;
  }

  /**
   * 判断是否为关键错误
   */
  isCriticalError(error) {
    // 智能修复阶段的错误通常不是关键的，除非是配置问题
    return error.message.includes('配置') || 
           error.message.includes('权限');
  }
}

module.exports = IntelligentRepairPhase;
